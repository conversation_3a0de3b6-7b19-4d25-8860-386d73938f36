import os
import csv
from pathlib import Path
from main import upLoadQn

def upload_images_and_generate_csv():
    """
    读取image目录内的图片，通过upLoadQn方法上传，
    上传完成后生成CSV表格
    """
    # 图片目录路径
    image_dir = Path("image")
    
    # 存储上传结果的列表
    upload_results = []
    
    # 遍历image目录下的所有图片文件
    for root, dirs, files in os.walk(image_dir):
        for file in files:
            # 只处理图片文件
            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                # 获取完整的本地文件路径
                local_file_path = os.path.join(root, file)
                
                # 计算相对于image目录的路径作为key
                # 例如: height_ob/age12.png 或 ob/age.png
                relative_path = os.path.relpath(local_file_path, image_dir)
                key = relative_path.replace('\\', '/')  # 确保使用正斜杠
                
                print(f"正在上传: {local_file_path} -> key: {key}")
                
                try:
                    # 调用upLoadQn方法上传文件
                    upLoadQn(key, local_file_path)
                    
                    # 生成URL（https://cimage.childrenworkout.com.cn + image/ + key）
                    url = f"https://cimage.childrenworkout.com.cn/image/{key}"
                    
                    # 获取不包含后缀的图片名称
                    filename_without_ext = os.path.splitext(file)[0]
                    
                    # 添加到结果列表
                    upload_results.append([url, filename_without_ext])
                    
                    print(f"上传成功: {key}")
                    
                except Exception as e:
                    print(f"上传失败 {key}: {str(e)}")
                    continue
    
    # 生成CSV文件
    csv_filename = "uploaded_images.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # 写入表头
        writer.writerow(['URL', '图片名称'])
        
        # 写入数据
        writer.writerows(upload_results)
    
    print(f"\nCSV文件已生成: {csv_filename}")
    print(f"共上传 {len(upload_results)} 张图片")

if __name__ == "__main__":
    upload_images_and_generate_csv()
