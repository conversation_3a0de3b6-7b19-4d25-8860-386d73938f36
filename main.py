import obs
from qiniu import Auth, put_file, etag

def upLoadQn(key, localfile):
    access_key = 'cWMLkIyGOc6M2dFKxuXPS6FYqOA8s8ECFANf6i5F'
    secret_key = '4frArJz78N5hz-WdZGd_T_pDZyKyk_zUExfOpQZt'
    # 构建鉴权对象
    q = Auth(access_key, secret_key)
    # 要上传的空间
    bucket_name = 'dailychildren'
    # 上传后保存的文件名
    # 生成上传 Token，可以指定过期时间等
    file_key = 'image/' + key
    policy = {
        'scope': f'{bucket_name}:{file_key}'
    }
    token = q.upload_token(bucket_name, file_key, 3600, policy)
    # 要上传文件的本地路径
    params = {'forceSaveKey': 'true'}
    ret, info = put_file(token, file_key, localfile, params, version='v2')
    print(info)
    assert ret['key'] == file_key
    assert ret['hash'] == etag(localfile)